-- =============================================
-- DATABASE SCHEMA MTQ LAMPUNG - OPTIMIZED VERSION
-- Berdasarkan database lama dengan perbaikan struktur dan normalisasi
-- =============================================

-- 1. MASTER WILAYAH (Perbaikan dari tbl_kabupaten)
CREATE TABLE wilayah (
    id_wilayah INT PRIMARY KEY AUTO_INCREMENT,
    kode_wilayah VARCHAR(10) UNIQUE NOT NULL,
    nama_wilayah VARCHAR(100) NOT NULL,
    level_wilayah ENUM('provinsi', 'kabupaten', 'kota') NOT NULL,
    parent_id INT NULL,
    status ENUM('aktif', 'non_aktif') DEFAULT 'aktif',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES wilayah(id_wilayah) ON DELETE SET NULL
);

-- 2. MASTER CABANG LOMBA (Perbaikan dari cabang)
CREATE TABLE cabang_lomba (
    id_cabang INT PRIMARY KEY AUTO_INCREMENT,
    kode_cabang VARCHAR(10) UNIQUE NOT NULL,
    nama_cabang VARCHAR(100) NOT NULL,
    deskripsi TEXT,
    status ENUM('aktif', 'non_aktif') DEFAULT 'aktif',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 3. MASTER GOLONGAN (Perbaikan dari golongan)
CREATE TABLE golongan (
    id_golongan INT PRIMARY KEY AUTO_INCREMENT,
    kode_golongan VARCHAR(10) UNIQUE NOT NULL,
    nama_golongan VARCHAR(100) NOT NULL,
    id_cabang INT NOT NULL,
    jenis_kelamin ENUM('L', 'P') NOT NULL,
    batas_umur_min INT NOT NULL,
    batas_umur_max INT NOT NULL,
    kuota_max INT DEFAULT 0,
    biaya_pendaftaran DECIMAL(10,2) DEFAULT 0,
    nomor_urut_awal INT DEFAULT 1,
    nomor_urut_akhir INT DEFAULT 999,
    status ENUM('aktif', 'non_aktif') DEFAULT 'aktif',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (id_cabang) REFERENCES cabang_lomba(id_cabang) ON DELETE CASCADE
);

-- 4. MASTER MIMBAR (Perbaikan dari mimbar)
CREATE TABLE mimbar (
    id_mimbar INT PRIMARY KEY AUTO_INCREMENT,
    kode_mimbar VARCHAR(10) UNIQUE NOT NULL,
    nama_mimbar VARCHAR(100) NOT NULL,
    keterangan TEXT,
    kapasitas INT DEFAULT 0,
    status ENUM('aktif', 'non_aktif') DEFAULT 'aktif',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 5. USERS - SISTEM MULTI ROLE (Perbaikan dari users)
CREATE TABLE users (
    id_user INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('superadmin', 'admin', 'admin_daerah', 'peserta', 'dewan_hakim') NOT NULL,
    id_wilayah INT NULL,
    nama_lengkap VARCHAR(100) NOT NULL,
    no_telepon VARCHAR(20),
    status ENUM('aktif', 'non_aktif', 'suspended') DEFAULT 'aktif',
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT NULL,
    FOREIGN KEY (id_wilayah) REFERENCES wilayah(id_wilayah) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id_user) ON DELETE SET NULL
);

-- 6. PESERTA (Perbaikan dari peserta - Normalisasi)
CREATE TABLE peserta (
    id_peserta INT PRIMARY KEY AUTO_INCREMENT,
    id_user INT UNIQUE NOT NULL,
    nik VARCHAR(16) UNIQUE NOT NULL,
    nama_lengkap VARCHAR(100) NOT NULL,
    tempat_lahir VARCHAR(50) NOT NULL,
    tanggal_lahir DATE NOT NULL,
    jenis_kelamin ENUM('L', 'P') NOT NULL,
    alamat TEXT NOT NULL,
    id_wilayah INT NOT NULL,
    no_telepon VARCHAR(20),
    email VARCHAR(100),
    nama_ayah VARCHAR(100),
    nama_ibu VARCHAR(100),
    pekerjaan VARCHAR(100),
    instansi_asal VARCHAR(100),
    -- Informasi pendaftaran
    registered_by INT NULL, -- ID admin yang mendaftarkan
    registration_type ENUM('mandiri', 'admin_daerah') DEFAULT 'mandiri',
    status_peserta ENUM('draft', 'submitted', 'verified', 'approved', 'rejected') DEFAULT 'draft',
    -- Informasi tambahan
    nama_rekening VARCHAR(100),
    no_rekening VARCHAR(40),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (id_user) REFERENCES users(id_user) ON DELETE CASCADE,
    FOREIGN KEY (id_wilayah) REFERENCES wilayah(id_wilayah) ON DELETE RESTRICT,
    FOREIGN KEY (registered_by) REFERENCES users(id_user) ON DELETE SET NULL
);

-- 7. PENDAFTARAN LOMBA (Menggabungkan konsep lama dengan struktur baru)
CREATE TABLE pendaftaran (
    id_pendaftaran INT PRIMARY KEY AUTO_INCREMENT,
    id_peserta INT NOT NULL,
    id_golongan INT NOT NULL,
    id_mimbar INT NULL,
    nomor_pendaftaran VARCHAR(20) UNIQUE NOT NULL,
    nomor_peserta VARCHAR(12) UNIQUE NOT NULL,
    tahun_pendaftaran YEAR NOT NULL,
    status_pendaftaran ENUM('draft', 'submitted', 'payment_pending', 'paid', 'verified', 'approved', 'rejected') DEFAULT 'draft',
    tanggal_daftar TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    -- Approval workflow
    verified_by INT NULL,
    verified_at TIMESTAMP NULL,
    approved_by INT NULL,
    approved_at TIMESTAMP NULL,
    catatan_verifikasi TEXT,
    catatan_approval TEXT,
    -- Informasi tambahan
    keterangan TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (id_peserta) REFERENCES peserta(id_peserta) ON DELETE CASCADE,
    FOREIGN KEY (id_golongan) REFERENCES golongan(id_golongan) ON DELETE RESTRICT,
    FOREIGN KEY (id_mimbar) REFERENCES mimbar(id_mimbar) ON DELETE SET NULL,
    FOREIGN KEY (verified_by) REFERENCES users(id_user) ON DELETE SET NULL,
    FOREIGN KEY (approved_by) REFERENCES users(id_user) ON DELETE SET NULL,
    UNIQUE KEY unique_peserta_golongan_tahun (id_peserta, id_golongan, tahun_pendaftaran)
);

-- 8. DOKUMEN PESERTA (Menggabungkan file, ktp, kartu_keluarga, foto)
CREATE TABLE dokumen_peserta (
    id_dokumen INT PRIMARY KEY AUTO_INCREMENT,
    id_pendaftaran INT NOT NULL,
    jenis_dokumen ENUM('foto', 'ktp', 'kartu_keluarga', 'surat_rekomendasi', 'ijazah', 'sertifikat', 'lainnya') NOT NULL,
    nama_file VARCHAR(255) NOT NULL,
    path_file VARCHAR(500) NOT NULL,
    ukuran_file INT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    status_verifikasi ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    catatan_verifikasi TEXT,
    verified_by INT NULL,
    verified_at TIMESTAMP NULL,
    uploaded_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (id_pendaftaran) REFERENCES pendaftaran(id_pendaftaran) ON DELETE CASCADE,
    FOREIGN KEY (verified_by) REFERENCES users(id_user) ON DELETE SET NULL,
    FOREIGN KEY (uploaded_by) REFERENCES users(id_user) ON DELETE RESTRICT
);

-- 9. PEMBAYARAN (Tabel baru untuk sistem pembayaran)
CREATE TABLE pembayaran (
    id_pembayaran INT PRIMARY KEY AUTO_INCREMENT,
    id_pendaftaran INT NOT NULL,
    nomor_transaksi VARCHAR(50) UNIQUE NOT NULL,
    jumlah_bayar DECIMAL(10,2) NOT NULL,
    metode_pembayaran ENUM('transfer_manual', 'va_bni', 'va_bri', 'va_mandiri', 'ovo', 'gopay', 'dana') NOT NULL,
    status_pembayaran ENUM('pending', 'paid', 'failed', 'expired') DEFAULT 'pending',
    tanggal_bayar TIMESTAMP NULL,
    bukti_pembayaran VARCHAR(255),
    reference_number VARCHAR(100),
    verified_by INT NULL,
    verified_at TIMESTAMP NULL,
    catatan TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (id_pendaftaran) REFERENCES pendaftaran(id_pendaftaran) ON DELETE CASCADE,
    FOREIGN KEY (verified_by) REFERENCES users(id_user) ON DELETE SET NULL
);

-- 10. DEWAN HAKIM (Perbaikan dari dewan_hakim dengan normalisasi)
CREATE TABLE dewan_hakim (
    id_dewan_hakim INT PRIMARY KEY AUTO_INCREMENT,
    id_user INT UNIQUE NOT NULL,
    nik VARCHAR(16) UNIQUE NOT NULL,
    nama_lengkap VARCHAR(100) NOT NULL,
    tempat_lahir VARCHAR(50) NOT NULL,
    tanggal_lahir DATE NOT NULL,
    pekerjaan VARCHAR(100),
    unit_kerja VARCHAR(100),
    alamat_rumah TEXT,
    alamat_kantor TEXT,
    no_telepon VARCHAR(20),
    spesialisasi TEXT,
    tipe_hakim ENUM('undangan', 'kabupaten') NOT NULL,
    id_wilayah INT NULL,
    status ENUM('aktif', 'non_aktif') DEFAULT 'aktif',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (id_user) REFERENCES users(id_user) ON DELETE CASCADE,
    FOREIGN KEY (id_wilayah) REFERENCES wilayah(id_wilayah) ON DELETE SET NULL
);

-- 11. DEWAN HAKIM PENDIDIKAN (Perbaikan dari dewan_hakim_pendidikan)
CREATE TABLE dewan_hakim_pendidikan (
    id_pendidikan INT PRIMARY KEY AUTO_INCREMENT,
    id_dewan_hakim INT NOT NULL,
    jenjang VARCHAR(10) NOT NULL,
    instansi VARCHAR(100) NOT NULL,
    jurusan VARCHAR(100) NOT NULL,
    tahun_mulai YEAR NOT NULL,
    tahun_lulus YEAR NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (id_dewan_hakim) REFERENCES dewan_hakim(id_dewan_hakim) ON DELETE CASCADE
);

-- 12. DEWAN HAKIM PENGALAMAN (Perbaikan dari dewan_hakim_pengalaman)
CREATE TABLE dewan_hakim_pengalaman (
    id_pengalaman INT PRIMARY KEY AUTO_INCREMENT,
    id_dewan_hakim INT NOT NULL,
    nama_kegiatan VARCHAR(200) NOT NULL,
    penyelenggara VARCHAR(100) NOT NULL,
    tahun YEAR NOT NULL,
    tingkat ENUM('kabupaten', 'provinsi', 'nasional', 'internasional') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (id_dewan_hakim) REFERENCES dewan_hakim(id_dewan_hakim) ON DELETE CASCADE
);

-- 13. DEWAN HAKIM PRESTASI (Perbaikan dari dewan_hakim_prestasi)
CREATE TABLE dewan_hakim_prestasi (
    id_prestasi INT PRIMARY KEY AUTO_INCREMENT,
    id_dewan_hakim INT NOT NULL,
    juara_ke INT NOT NULL,
    cabang_golongan VARCHAR(100) NOT NULL,
    nama_kegiatan VARCHAR(200) NOT NULL,
    tahun YEAR NOT NULL,
    tingkat ENUM('kabupaten', 'provinsi', 'nasional', 'internasional') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (id_dewan_hakim) REFERENCES dewan_hakim(id_dewan_hakim) ON DELETE CASCADE
);

-- 14. JENIS NILAI (Perbaikan dari jenis_nilai)
CREATE TABLE jenis_nilai (
    id_jenis_nilai INT PRIMARY KEY AUTO_INCREMENT,
    nama_jenis VARCHAR(100) NOT NULL,
    kode_jenis VARCHAR(20) UNIQUE NOT NULL,
    keterangan TEXT,
    bobot_nilai DECIMAL(5,2) DEFAULT 1.00,
    nilai_minimum DECIMAL(5,2) DEFAULT 0,
    nilai_maksimum DECIMAL(5,2) DEFAULT 100,
    status ENUM('aktif', 'non_aktif') DEFAULT 'aktif',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 15. NILAI PESERTA (Perbaikan dari nilai)
CREATE TABLE nilai_peserta (
    id_nilai INT PRIMARY KEY AUTO_INCREMENT,
    id_pendaftaran INT NOT NULL,
    id_jenis_nilai INT NOT NULL,
    id_dewan_hakim INT NOT NULL,
    nilai DECIMAL(5,2) NOT NULL,
    catatan TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (id_pendaftaran) REFERENCES pendaftaran(id_pendaftaran) ON DELETE CASCADE,
    FOREIGN KEY (id_jenis_nilai) REFERENCES jenis_nilai(id_jenis_nilai) ON DELETE RESTRICT,
    FOREIGN KEY (id_dewan_hakim) REFERENCES dewan_hakim(id_dewan_hakim) ON DELETE RESTRICT,
    UNIQUE KEY unique_nilai_peserta (id_pendaftaran, id_jenis_nilai, id_dewan_hakim)
);

-- 16. SURAT MANDAT (Perbaikan dari surat_mandat)
CREATE TABLE surat_mandat (
    id_surat_mandat INT PRIMARY KEY AUTO_INCREMENT,
    id_wilayah INT NOT NULL,
    nomor_surat VARCHAR(100) NOT NULL,
    tanggal_surat DATE NOT NULL,
    path_file VARCHAR(500) NOT NULL,
    keterangan TEXT,
    status ENUM('aktif', 'non_aktif') DEFAULT 'aktif',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (id_wilayah) REFERENCES wilayah(id_wilayah) ON DELETE CASCADE
);

-- 17. PELAKSANAAN MTQ (Perbaikan dari pelaksanaan)
CREATE TABLE pelaksanaan (
    id_pelaksanaan INT PRIMARY KEY AUTO_INCREMENT,
    tahun YEAR UNIQUE NOT NULL,
    tema VARCHAR(200) NOT NULL,
    tempat VARCHAR(100) NOT NULL,
    tanggal_mulai DATE NOT NULL,
    tanggal_selesai DATE NOT NULL,
    tanggal_buka_pendaftaran DATE NOT NULL,
    tanggal_tutup_pendaftaran DATE NOT NULL,
    status ENUM('draft', 'aktif', 'selesai') DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 18. LOG AKTIVITAS (Perbaikan dari log_delete)
CREATE TABLE log_aktivitas (
    id_log INT PRIMARY KEY AUTO_INCREMENT,
    id_user INT NOT NULL,
    aktivitas VARCHAR(255) NOT NULL,
    modul VARCHAR(50) NOT NULL,
    detail_aktivitas TEXT,
    data_lama JSON,
    data_baru JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (id_user) REFERENCES users(id_user) ON DELETE CASCADE
);

-- 19. NOTIFIKASI
CREATE TABLE notifikasi (
    id_notifikasi INT PRIMARY KEY AUTO_INCREMENT,
    id_user INT NOT NULL,
    judul VARCHAR(255) NOT NULL,
    pesan TEXT NOT NULL,
    jenis ENUM('info', 'warning', 'success', 'error') DEFAULT 'info',
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP NULL,
    FOREIGN KEY (id_user) REFERENCES users(id_user) ON DELETE CASCADE
);

-- 20. PENGATURAN SISTEM (Perbaikan dari setting)
CREATE TABLE pengaturan (
    id_pengaturan INT PRIMARY KEY AUTO_INCREMENT,
    key_setting VARCHAR(100) UNIQUE NOT NULL,
    value_setting TEXT NOT NULL,
    deskripsi TEXT,
    kategori VARCHAR(50) DEFAULT 'umum',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- =============================================
-- INDEXES UNTUK OPTIMASI PERFORMA
-- =============================================

-- Index untuk pencarian yang sering dilakukan
CREATE INDEX idx_peserta_nik ON peserta(nik);
CREATE INDEX idx_peserta_nama ON peserta(nama_lengkap);
CREATE INDEX idx_peserta_wilayah ON peserta(id_wilayah);
CREATE INDEX idx_peserta_status ON peserta(status_peserta);

CREATE INDEX idx_pendaftaran_nomor ON pendaftaran(nomor_pendaftaran);
CREATE INDEX idx_pendaftaran_peserta ON pendaftaran(nomor_peserta);
CREATE INDEX idx_pendaftaran_status ON pendaftaran(status_pendaftaran);
CREATE INDEX idx_pendaftaran_tahun ON pendaftaran(tahun_pendaftaran);

CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_wilayah ON users(id_wilayah);
CREATE INDEX idx_users_status ON users(status);

CREATE INDEX idx_dokumen_jenis ON dokumen_peserta(jenis_dokumen);
CREATE INDEX idx_dokumen_status ON dokumen_peserta(status_verifikasi);

CREATE INDEX idx_pembayaran_status ON pembayaran(status_pembayaran);
CREATE INDEX idx_pembayaran_nomor ON pembayaran(nomor_transaksi);

CREATE INDEX idx_nilai_pendaftaran ON nilai_peserta(id_pendaftaran);
CREATE INDEX idx_log_user ON log_aktivitas(id_user);
CREATE INDEX idx_log_created ON log_aktivitas(created_at);

-- =============================================
-- VIEWS UNTUK KEMUDAHAN QUERY
-- =============================================

-- View untuk melihat data lengkap peserta
CREATE VIEW view_peserta_lengkap AS
SELECT 
    p.id_peserta,
    p.nik,
    p.nama_lengkap,
    p.tempat_lahir,
    p.tanggal_lahir,
    p.jenis_kelamin,
    w.nama_wilayah,
    p.status_peserta,
    u.username,
    u.email,
    CASE 
        WHEN p.registered_by IS NOT NULL THEN 'Admin Daerah'
        ELSE 'Mandiri'
    END as cara_pendaftaran,
    p.created_at
FROM peserta p
JOIN wilayah w ON p.id_wilayah = w.id_wilayah
JOIN users u ON p.id_user = u.id_user;

-- View untuk melihat pendaftaran dengan detail
CREATE VIEW view_pendaftaran_detail AS
SELECT 
    pd.id_pendaftaran,
    pd.nomor_pendaftaran,
    pd.nomor_peserta,
    p.nama_lengkap,
    p.nik,
    cl.nama_cabang,
    g.nama_golongan,
    w.nama_wilayah,
    pd.status_pendaftaran,
    pd.tanggal_daftar,
    py.status_pembayaran,
    py.jumlah_bayar
FROM pendaftaran pd
JOIN peserta p ON pd.id_peserta = p.id_peserta
JOIN golongan g ON pd.id_golongan = g.id_golongan
JOIN cabang_lomba cl ON g.id_cabang = cl.id_cabang
JOIN wilayah w ON p.id_wilayah = w.id_wilayah
LEFT JOIN pembayaran py ON pd.id_pendaftaran = py.id_pendaftaran;
