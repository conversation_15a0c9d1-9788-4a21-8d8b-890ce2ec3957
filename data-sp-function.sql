
-- =============================================
-- STORED PROCEDURES & FUNCTIONS
-- =============================================

-- Function untuk generate nomor pendaftaran
DELIMITER //
CREATE FUNCTION generate_nomor_pendaftaran(
    p_id_wilayah INT,
    p_id_golongan INT,
    p_tahun YEAR
) RETURNS VARCHAR(20)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE nomor_urut INT;
    DECLARE kode_wilayah VARCHAR(10);
    DECLARE kode_golongan VARCHAR(10);
    DECLARE nomor_pendaftaran VARCHAR(20);
    
    SELECT w.kode_wilayah INTO kode_wilayah 
    FROM wilayah w WHERE w.id_wilayah = p_id_wilayah;
    
    SELECT g.kode_golongan INTO kode_golongan 
    FROM golongan g WHERE g.id_golongan = p_id_golongan;
    
    SELECT COALESCE(MAX(CAST(SUBSTRING(nomor_pendaftaran, -4) AS UNSIGNED)), 0) + 1 
    INTO nomor_urut
    FROM pendaftaran pd
    JOIN peserta pe ON pd.id_peserta = pe.id_peserta
    WHERE pe.id_wilayah = p_id_wilayah 
    AND pd.id_golongan = p_id_golongan
    AND pd.tahun_pendaftaran = p_tahun;
    
    SET nomor_pendaftaran = CONCAT(p_tahun, '-', kode_wilayah, '-', kode_golongan, '-', LPAD(nomor_urut, 4, '0'));
    
    RETURN nomor_pendaftaran;
END //
DELIMITER ;

-- Function untuk generate nomor peserta
DELIMITER //
CREATE FUNCTION generate_nomor_peserta(
    p_id_golongan INT,
    p_tahun YEAR
) RETURNS VARCHAR(12)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE nomor_urut INT;
    DECLARE kode_golongan VARCHAR(10);
    DECLARE nomor_peserta VARCHAR(12);
    
    SELECT g.kode_golongan INTO kode_golongan 
    FROM golongan g WHERE g.id_golongan = p_id_golongan;
    
    SELECT COALESCE(MAX(CAST(SUBSTRING(nomor_peserta, -3) AS UNSIGNED)), 0) + 1 
    INTO nomor_urut
    FROM pendaftaran 
    WHERE id_golongan = p_id_golongan
    AND tahun_pendaftaran = p_tahun;
    
    SET nomor_peserta = CONCAT(kode_golongan, '-', p_tahun, '-', LPAD(nomor_urut, 3, '0'));
    
    RETURN nomor_peserta;
END //
DELIMITER ;

-- =============================================
-- TRIGGERS UNTUK AUDIT TRAIL
-- =============================================

-- Trigger untuk log aktivitas insert peserta
DELIMITER //
CREATE TRIGGER tr_peserta_insert 
    AFTER INSERT ON peserta
    FOR EACH ROW
BEGIN
    INSERT INTO log_aktivitas (id_user, aktivitas, modul, detail_aktivitas, data_baru)
    VALUES (NEW.id_user, 'INSERT', 'peserta', 
            CONCAT('Mendaftarkan peserta baru: ', NEW.nama_lengkap),
            JSON_OBJECT('id_peserta', NEW.id_peserta, 'nama', NEW.nama_lengkap, 'nik', NEW.nik));
END //
DELIMITER ;

-- Trigger untuk log aktivitas update peserta
DELIMITER //
CREATE TRIGGER tr_peserta_update 
    AFTER UPDATE ON peserta
    FOR EACH ROW
BEGIN
    INSERT INTO log_aktivitas (id_user, aktivitas, modul, detail_aktivitas, data_lama, data_baru)
    VALUES (NEW.id_user, 'UPDATE', 'peserta', 
            CONCAT('Mengupdate data peserta: ', NEW.nama_lengkap),
            JSON_OBJECT('status_lama', OLD.status_peserta),
            JSON_OBJECT('status_baru', NEW.status_peserta));
END //
DELIMITER ;

-- =============================================
-- INITIAL DATA
-- =============================================

-- Insert wilayah Lampung
INSERT INTO wilayah (kode_wilayah, nama_wilayah, level_wilayah, parent_id) VALUES
('18', 'Lampung', 'provinsi', NULL),
('1801', 'Lampung Barat', 'kabupaten', 1),
('1802', 'Lampung Selatan', 'kabupaten', 1),
('1803', 'Lampung Tengah', 'kabupaten', 1),
('1804', 'Lampung Utara', 'kabupaten', 1),
('1805', 'Lampung Timur', 'kabupaten', 1),
('1806', 'Way Kanan', 'kabupaten', 1),
('1807', 'Tulang Bawang', 'kabupaten', 1),
('1808', 'Pesawaran', 'kabupaten', 1),
('1809', 'Pringsewu', 'kabupaten', 1),
('1810', 'Mesuji', 'kabupaten', 1),
('1811', 'Tulang Bawang Barat', 'kabupaten', 1),
('1812', 'Pesisir Barat', 'kabupaten', 1),
('1871', 'Bandar Lampung', 'kota', 1),
('1872', 'Metro', 'kota', 1);

-- Insert cabang lomba
INSERT INTO cabang_lomba (kode_cabang, nama_cabang, deskripsi) VALUES
('TIL', 'Tilawatil Quran', 'Seni membaca Al-Quran dengan tartil'),
('TAH', 'Tahfidzul Quran', 'Hafalan Al-Quran'),
('FAH', 'Fahmil Quran', 'Pemahaman isi Al-Quran'),
('SYA', 'Syarhil Quran', 'Tafsir Al-Quran'),
('KAL', 'Kaligrafi', 'Seni tulis Arab'),
('NAS', 'Nasyid', 'Seni musik Islami'),
('CER', 'Ceramah', 'Dakwah dan khutbah'),
('QIS', 'Qiraatul Kutub', 'Pembacaan kitab kuning');

-- Insert golongan untuk beberapa cabang
INSERT INTO golongan (kode_golongan, nama_golongan, id_cabang, jenis_kelamin, batas_umur_min, batas_umur_max, biaya_pendaftaran) VALUES
('TIL-PA', 'Tilawah Putra Anak-anak', 1, 'L', 10, 12, 50000),
('TIL-PI', 'Tilawah Putri Anak-anak', 1, 'P', 10, 12, 50000),
('TIL-PR', 'Tilawah Putra Remaja', 1, 'L', 13, 16, 75000),
('TIL-PiR', 'Tilawah Putri Remaja', 1, 'P', 13, 16, 75000),
('TIL-PD', 'Tilawah Putra Dewasa', 1, 'L', 17, 35, 100000),
('TIL-PiD', 'Tilawah Putri Dewasa', 1, 'P', 17, 35, 100000),
('TAH-5P', 'Tahfidz 5 Juz Putra', 2, 'L', 15, 25, 100000),
('TAH-5Pi', 'Tahfidz 5 Juz Putri', 2, 'P', 15, 25, 100000),
('TAH-10P', 'Tahfidz 10 Juz Putra', 2, 'L', 18, 30, 150000),
('TAH-10Pi', 'Tahfidz 10 Juz Putri', 2, 'P', 18, 30, 150000);

-- Insert mimbar
INSERT INTO mimbar (kode_mimbar, nama_mimbar, kapasitas) VALUES
('A', 'Mimbar A', 50),
('B', 'Mimbar B', 50),
('C', 'Mimbar C', 50),
('D', 'Mimbar D', 50);


-- Insert jenis nilai
INSERT INTO jenis_nilai (nama_jenis, kode_jenis, keterangan, bobot_nilai, nilai_maksimum) VALUES
('Fashohah', 'FAS', 'Kebenaran dalam membaca', 1.0, 100),
('Nagham', 'NAG', 'Keindahan suara dan lagu', 1.0, 100),
('Tajwid', 'TAJ', 'Kaidah tajwid', 1.0, 100),
('Adab', 'ADA', 'Etika dan sopan santun', 1.0, 100),
('Penampilan', 'PEN', 'Penampilan dan busana', 1.0, 100),
('Penguasaan Materi', 'PEN_MAT', 'Penguasaan materi yang disampaikan', 1.0, 100),
('Sistematika', 'SIS', 'Sistematika penyampaian', 1.0, 100),
('Bahasa', 'BAH', 'Penggunaan bahasa yang baik', 1.0, 100),
('Kreativitas', 'KRE', 'Kreativitas dalam penyampaian', 1.0, 100),
('Ketepatan Waktu', 'WAK', 'Ketepatan waktu dalam penyampaian', 1.0, 100);

-- Insert user default (superadmin)
INSERT INTO users (username, email, password, role, nama_lengkap, status) VALUES
('superadmin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'superadmin', 'Administrator MTQ Lampung', 'aktif'),
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 'Admin MTQ Lampung', 'aktif');

-- Insert pengaturan sistem
INSERT INTO pengaturan (key_setting, value_setting, deskripsi, kategori) VALUES
('app_name', 'MTQ Lampung', 'Nama aplikasi', 'aplikasi'),
('app_version', '2.0.0', 'Versi aplikasi', 'aplikasi'),
('app_description', 'Sistem Informasi Musabaqah Tilawatil Quran Provinsi Lampung', 'Deskripsi aplikasi', 'aplikasi'),
('contact_email', '<EMAIL>', 'Email kontak', 'kontak'),
('contact_phone', '0721-123456', 'Nomor telepon kontak', 'kontak'),
('contact_address', 'Jl. Zainal Abidin Pagar Alam No. 1, Bandar Lampung', 'Alamat kontak', 'kontak'),
('max_file_size', '5242880', 'Ukuran maksimal file upload (5MB)', 'upload'),
('allowed_file_types', 'jpg,jpeg,png,pdf', 'Jenis file yang diizinkan', 'upload'),
('registration_open', '1', 'Status pendaftaran (1=buka, 0=tutup)', 'pendaftaran'),
('current_year', '2024', 'Tahun pelaksanaan saat ini', 'pelaksanaan'),
('payment_methods', 'transfer_manual,va_bni,va_bri', 'Metode pembayaran yang aktif', 'pembayaran'),
('bank_account_bni', '**********', 'Nomor rekening BNI', 'pembayaran'),
('bank_account_bri', '**********', 'Nomor rekening BRI', 'pembayaran'),
('bank_account_mandiri', '**********', 'Nomor rekening Mandiri', 'pembayaran'),
('bank_account_name', 'Panitia MTQ Lampung', 'Nama pemilik rekening', 'pembayaran'),
('smtp_host', 'smtp.gmail.com', 'SMTP Host untuk email', 'email'),
('smtp_port', '587', 'SMTP Port', 'email'),
('smtp_username', '<EMAIL>', 'SMTP Username', 'email'),
('smtp_password', '', 'SMTP Password', 'email'),
('smtp_encryption', 'tls', 'SMTP Encryption', 'email'),
('notification_email', '1', 'Aktifkan notifikasi email', 'notifikasi'),
('notification_sms', '0', 'Aktifkan notifikasi SMS', 'notifikasi'),
('timezone', 'Asia/Jakarta', 'Zona waktu', 'sistem'),
('date_format', 'd-m-Y', 'Format tanggal', 'sistem'),
('time_format', 'H:i', 'Format waktu', 'sistem'),
('currency_symbol', 'Rp', 'Simbol mata uang', 'sistem'),
('decimal_separator', ',', 'Pemisah desimal', 'sistem'),
('thousand_separator', '.', 'Pemisah ribuan', 'sistem');

-- Insert pelaksanaan default
INSERT INTO pelaksanaan (tahun, tema, tempat, tanggal_mulai, tanggal_selesai, tanggal_buka_pendaftaran, tanggal_tutup_pendaftaran, status) VALUES
('2024', 'Al-Quran Pedoman Hidup Menuju Masyarakat Madani', 'Bandar Lampung', '2024-08-15', '2024-08-20', '2024-06-01', '2024-07-31', 'aktif');

-- =============================================
-- ADDITIONAL STORED PROCEDURES
-- =============================================

-- Procedure untuk mendaftarkan peserta
DELIMITER //
CREATE PROCEDURE sp_daftar_peserta(
    IN p_id_peserta INT,
    IN p_id_golongan INT,
    IN p_tahun YEAR,
    IN p_keterangan TEXT,
    OUT p_nomor_pendaftaran VARCHAR(20),
    OUT p_nomor_peserta VARCHAR(12),
    OUT p_status VARCHAR(20)
)
BEGIN
    DECLARE v_id_wilayah INT;
    DECLARE v_kuota_max INT;
    DECLARE v_jumlah_terdaftar INT;
    DECLARE v_biaya DECIMAL(10,2);
    DECLARE v_id_pendaftaran INT;
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_status = 'ERROR';
        GET DIAGNOSTICS CONDITION 1
            @sqlstate = RETURNED_SQLSTATE,
            @errno = MYSQL_ERRNO,
            @text = MESSAGE_TEXT;
        SET p_nomor_pendaftaran = @text;
    END;
    
    START TRANSACTION;
    
    -- Get wilayah peserta
    SELECT id_wilayah INTO v_id_wilayah 
    FROM peserta WHERE id_peserta = p_id_peserta;
    
    -- Check kuota
    SELECT kuota_max, biaya_pendaftaran INTO v_kuota_max, v_biaya
    FROM golongan WHERE id_golongan = p_id_golongan;
    
    SELECT COUNT(*) INTO v_jumlah_terdaftar
    FROM pendaftaran pd
    JOIN peserta pe ON pd.id_peserta = pe.id_peserta
    WHERE pd.id_golongan = p_id_golongan 
    AND pd.tahun_pendaftaran = p_tahun
    AND pd.status_pendaftaran NOT IN ('rejected', 'draft');
    
    IF v_kuota_max > 0 AND v_jumlah_terdaftar >= v_kuota_max THEN
        SET p_status = 'KUOTA_PENUH';
        ROLLBACK;
    ELSE
        -- Generate nomor
        SET p_nomor_pendaftaran = generate_nomor_pendaftaran(v_id_wilayah, p_id_golongan, p_tahun);
        SET p_nomor_peserta = generate_nomor_peserta(p_id_golongan, p_tahun);
        
        -- Insert pendaftaran
        INSERT INTO pendaftaran (
            id_peserta, id_golongan, nomor_pendaftaran, nomor_peserta, 
            tahun_pendaftaran, status_pendaftaran, keterangan
        ) VALUES (
            p_id_peserta, p_id_golongan, p_nomor_pendaftaran, p_nomor_peserta,
            p_tahun, 'submitted', p_keterangan
        );
        
        SET v_id_pendaftaran = LAST_INSERT_ID();
        
        -- Insert pembayaran record
        INSERT INTO pembayaran (
            id_pendaftaran, nomor_transaksi, jumlah_bayar, 
            metode_pembayaran, status_pembayaran
        ) VALUES (
            v_id_pendaftaran, 
            CONCAT('TRX-', p_tahun, '-', LPAD(v_id_pendaftaran, 6, '0')),
            v_biaya, 'transfer_manual', 'pending'
        );
        
        SET p_status = 'SUCCESS';
        COMMIT;
    END IF;
    
END //
DELIMITER ;

-- Procedure untuk verifikasi dokumen
DELIMITER //
CREATE PROCEDURE sp_verifikasi_dokumen(
    IN p_id_dokumen INT,
    IN p_status_verifikasi ENUM('approved', 'rejected'),
    IN p_catatan TEXT,
    IN p_verified_by INT
)
BEGIN
    DECLARE v_id_pendaftaran INT;
    DECLARE v_total_dokumen INT;
    DECLARE v_dokumen_approved INT;
    
    -- Update status dokumen
    UPDATE dokumen_peserta 
    SET status_verifikasi = p_status_verifikasi,
        catatan_verifikasi = p_catatan,
        verified_by = p_verified_by,
        verified_at = CURRENT_TIMESTAMP
    WHERE id_dokumen = p_id_dokumen;
    
    -- Get id_pendaftaran
    SELECT id_pendaftaran INTO v_id_pendaftaran
    FROM dokumen_peserta WHERE id_dokumen = p_id_dokumen;
    
    -- Check if all documents are verified
    SELECT COUNT(*) INTO v_total_dokumen
    FROM dokumen_peserta 
    WHERE id_pendaftaran = v_id_pendaftaran;
    
    SELECT COUNT(*) INTO v_dokumen_approved
    FROM dokumen_peserta 
    WHERE id_pendaftaran = v_id_pendaftaran 
    AND status_verifikasi = 'approved';
    
    -- Update status pendaftaran if all documents approved
    IF v_total_dokumen = v_dokumen_approved THEN
        UPDATE pendaftaran 
        SET status_pendaftaran = 'verified',
            verified_by = p_verified_by,
            verified_at = CURRENT_TIMESTAMP
        WHERE id_pendaftaran = v_id_pendaftaran;
    END IF;
    
END //
DELIMITER ;

-- Procedure untuk menghitung ranking
DELIMITER //
CREATE PROCEDURE sp_hitung_ranking(
    IN p_id_golongan INT,
    IN p_tahun YEAR
)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_id_pendaftaran INT;
    DECLARE v_total_nilai DECIMAL(10,2);
    DECLARE v_rank INT DEFAULT 1;
    
    DECLARE cur_peserta CURSOR FOR
        SELECT pd.id_pendaftaran, AVG(np.nilai) as rata_nilai
        FROM pendaftaran pd
        JOIN nilai_peserta np ON pd.id_pendaftaran = np.id_pendaftaran
        WHERE pd.id_golongan = p_id_golongan
        AND pd.tahun_pendaftaran = p_tahun
        AND pd.status_pendaftaran = 'approved'
        GROUP BY pd.id_pendaftaran
        ORDER BY rata_nilai DESC;
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    -- Create temporary table for ranking
    CREATE TEMPORARY TABLE temp_ranking (
        id_pendaftaran INT,
        total_nilai DECIMAL(10,2),
        ranking INT
    );
    
    OPEN cur_peserta;
    
    read_loop: LOOP
        FETCH cur_peserta INTO v_id_pendaftaran, v_total_nilai;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        INSERT INTO temp_ranking VALUES (v_id_pendaftaran, v_total_nilai, v_rank);
        SET v_rank = v_rank + 1;
    END LOOP;
    
    CLOSE cur_peserta;
    
    -- Return ranking results
    SELECT 
        tr.ranking,
        pd.nomor_peserta,
        pe.nama_lengkap,
        w.nama_wilayah,
        tr.total_nilai
    FROM temp_ranking tr
    JOIN pendaftaran pd ON tr.id_pendaftaran = pd.id_pendaftaran
    JOIN peserta pe ON pd.id_peserta = pe.id_peserta
    JOIN wilayah w ON pe.id_wilayah = w.id_wilayah
    ORDER BY tr.ranking;
    
    DROP TEMPORARY TABLE temp_ranking;
    
END //
DELIMITER ;

-- =============================================
-- ADDITIONAL TRIGGERS
-- =============================================

-- Trigger untuk update status pendaftaran setelah pembayaran
DELIMITER //
CREATE TRIGGER tr_pembayaran_update
    AFTER UPDATE ON pembayaran
    FOR EACH ROW
BEGIN
    IF NEW.status_pembayaran = 'paid' AND OLD.status_pembayaran != 'paid' THEN
        UPDATE pendaftaran 
        SET status_pendaftaran = 'paid'
        WHERE id_pendaftaran = NEW.id_pendaftaran;
    END IF;
END //
DELIMITER ;

-- Trigger untuk notifikasi otomatis
DELIMITER //
CREATE TRIGGER tr_pendaftaran_notification
    AFTER UPDATE ON pendaftaran
    FOR EACH ROW
BEGIN
    DECLARE v_id_user INT;
    DECLARE v_judul VARCHAR(255);
    DECLARE v_pesan TEXT;
    
    SELECT id_user INTO v_id_user
    FROM peserta WHERE id_peserta = NEW.id_peserta;
    
    IF NEW.status_pendaftaran != OLD.status_pendaftaran THEN
        CASE NEW.status_pendaftaran
            WHEN 'verified' THEN
                SET v_judul = 'Pendaftaran Terverifikasi';
                SET v_pesan = CONCAT('Pendaftaran Anda dengan nomor ', NEW.nomor_pendaftaran, ' telah terverifikasi.');
            WHEN 'approved' THEN
                SET v_judul = 'Pendaftaran Disetujui';
                SET v_pesan = CONCAT('Selamat! Pendaftaran Anda dengan nomor ', NEW.nomor_pendaftaran, ' telah disetujui.');
            WHEN 'rejected' THEN
                SET v_judul = 'Pendaftaran Ditolak';
                SET v_pesan = CONCAT('Pendaftaran Anda dengan nomor ', NEW.nomor_pendaftaran, ' ditolak. Silakan hubungi admin untuk informasi lebih lanjut.');
        END CASE;
        
        INSERT INTO notifikasi (id_user, judul, pesan, jenis)
        VALUES (v_id_user, v_judul, v_pesan, 'info');
    END IF;
END //
DELIMITER ;

-- =============================================
-- ADDITIONAL VIEWS
-- =============================================

-- View untuk dashboard admin
CREATE VIEW view_dashboard_admin AS
SELECT 
    (SELECT COUNT(*) FROM peserta WHERE status_peserta = 'approved') as total_peserta,
    (SELECT COUNT(*) FROM pendaftaran WHERE status_pendaftaran = 'submitted') as pendaftaran_baru,
    (SELECT COUNT(*) FROM pendaftaran WHERE status_pendaftaran = 'verified') as menunggu_approval,
    (SELECT COUNT(*) FROM pembayaran WHERE status_pembayaran = 'pending') as pembayaran_pending,
    (SELECT COUNT(*) FROM dokumen_peserta WHERE status_verifikasi = 'pending') as dokumen_pending,
    (SELECT SUM(jumlah_bayar) FROM pembayaran WHERE status_pembayaran = 'paid') as total_pembayaran;

-- View untuk statistik wilayah
CREATE VIEW view_statistik_wilayah AS
SELECT 
    w.nama_wilayah,
    COUNT(DISTINCT p.id_peserta) as total_peserta,
    COUNT(DISTINCT pd.id_pendaftaran) as total_pendaftaran,
    COUNT(DISTINCT CASE WHEN pd.status_pendaftaran = 'approved' THEN pd.id_pendaftaran END) as pendaftaran_approved,
    COALESCE(SUM(CASE WHEN py.status_pembayaran = 'paid' THEN py.jumlah_bayar END), 0) as total_pembayaran
FROM wilayah w
LEFT JOIN peserta p ON w.id_wilayah = p.id_wilayah
LEFT JOIN pendaftaran pd ON p.id_peserta = pd.id_peserta
LEFT JOIN pembayaran py ON pd.id_pendaftaran = py.id_pendaftaran
WHERE w.level_wilayah IN ('kabupaten', 'kota')
GROUP BY w.id_wilayah, w.nama_wilayah
ORDER BY total_peserta DESC;

-- View untuk ranking peserta
CREATE VIEW view_ranking_peserta AS
SELECT 
    pd.id_pendaftaran,
    pd.nomor_peserta,
    pe.nama_lengkap,
    w.nama_wilayah,
    cl.nama_cabang,
    g.nama_golongan,
    AVG(np.nilai) as rata_nilai,
    COUNT(np.id_nilai) as jumlah_nilai,
    RANK() OVER (PARTITION BY pd.id_golongan ORDER BY AVG(np.nilai) DESC) as ranking
FROM pendaftaran pd
JOIN peserta pe ON pd.id_peserta = pe.id_peserta
JOIN wilayah w ON pe.id_wilayah = w.id_wilayah
JOIN golongan g ON pd.id_golongan = g.id_golongan
JOIN cabang_lomba cl ON g.id_cabang = cl.id_cabang
LEFT JOIN nilai_peserta np ON pd.id_pendaftaran = np.id_pendaftaran
WHERE pd.status_pendaftaran = 'approved'
GROUP BY pd.id_pendaftaran, pd.nomor_peserta, pe.nama_lengkap, w.nama_wilayah, cl.nama_cabang, g.nama_golongan
HAVING COUNT(np.id_nilai) > 0;

-- =============================================
-- ADDITIONAL FUNCTIONS
-- =============================================

-- Function untuk menghitung umur
DELIMITER //
CREATE FUNCTION hitung_umur(tanggal_lahir DATE) 
RETURNS INT
READS SQL DATA
DETERMINISTIC
BEGIN
    RETURN TIMESTAMPDIFF(YEAR, tanggal_lahir, CURDATE());
END //
DELIMITER ;

-- Function untuk validasi NIK
DELIMITER //
CREATE FUNCTION validasi_nik(nik VARCHAR(16)) 
RETURNS BOOLEAN
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE valid BOOLEAN DEFAULT FALSE;
    
    IF LENGTH(nik) = 16 AND nik REGEXP '^[0-9]+$' THEN
        SET valid = TRUE;
    END IF;
    
    RETURN valid;
END //
DELIMITER ;

-- Function untuk format nomor telepon
DELIMITER //
CREATE FUNCTION format_telepon(nomor VARCHAR(20)) 
RETURNS VARCHAR(20)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE formatted VARCHAR(20);
    
    SET formatted = REPLACE(REPLACE(REPLACE(nomor, '-', ''), ' ', ''), '(', '');
    SET formatted = REPLACE(formatted, ')', '');
    
    IF LEFT(formatted, 1) = '0' THEN
        SET formatted = CONCAT('62', SUBSTRING(formatted, 2));
    ELSEIF LEFT(formatted, 2) != '62' THEN
        SET formatted = CONCAT('62', formatted);
    END IF;
    
    RETURN formatted;
END //
DELIMITER ;

-- =============================================
-- FINAL INDEXES & CONSTRAINTS
-- =============================================

-- Additional composite indexes for better performance
CREATE INDEX idx_pendaftaran_composite ON pendaftaran(id_golongan, tahun_pendaftaran, status_pendaftaran);
CREATE INDEX idx_nilai_composite ON nilai_peserta(id_pendaftaran, id_jenis_nilai);
CREATE INDEX idx_dokumen_composite ON dokumen_peserta(id_pendaftaran, jenis_dokumen, status_verifikasi);
CREATE INDEX idx_pembayaran_composite ON pembayaran(id_pendaftaran, status_pembayaran);
CREATE INDEX idx_peserta_composite ON peserta(id_wilayah, status_peserta);

-- Add check constraints for data integrity
ALTER TABLE peserta ADD CONSTRAINT chk_nik_length CHECK (LENGTH(nik) = 16);
ALTER TABLE peserta ADD CONSTRAINT chk_jenis_kelamin CHECK (jenis_kelamin IN ('L', 'P'));
ALTER TABLE golongan ADD CONSTRAINT chk_batas_umur CHECK (batas_umur_min <= batas_umur_max);
ALTER TABLE golongan ADD CONSTRAINT chk_kuota_positive CHECK (kuota_max >= 0);
ALTER TABLE pembayaran ADD CONSTRAINT chk_jumlah_bayar_positive CHECK (jumlah_bayar > 0);
ALTER TABLE nilai_peserta ADD CONSTRAINT chk_nilai_range CHECK (nilai >= 0 AND nilai <= 100);

-- =============================================
-- SAMPLE DATA FOR TESTING
-- =============================================

-- Insert sample dewan hakim
INSERT INTO users (username, email, password, role, nama_lengkap, no_telepon, status) VALUES
('hakim001', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'dewan_hakim', 'Prof. Dr. Ahmad Syarifuddin', '08123456789', 'aktif'),
('hakim002', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'dewan_hakim', 'Dr. Hj. Fatimah Al-Zahra', '08123456790', 'aktif'),
('hakim003', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'dewan_hakim', 'Ustadz Muhammad Ridwan', '08123456791', 'aktif');

-- Insert sample admin daerah
INSERT INTO users (username, email, password, role, id_wilayah, nama_lengkap, no_telepon, status) VALUES
('admin_bdl', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin_daerah', 13, 'Admin Bandar Lampung', '08111222333', 'aktif'),
('admin_metro', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin_daerah', 14, 'Admin Metro', '08111222334', 'aktif'),
('admin_lamsel', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin_daerah', 2, 'Admin Lampung Selatan', '08111222335', 'aktif');

-- Insert sample dewan hakim profiles
INSERT INTO dewan_hakim (id_user, nik, nama_lengkap, tempat_lahir, tanggal_lahir, pekerjaan, unit_kerja, alamat_rumah, no_telepon, tipe_hakim, id_wilayah) VALUES
(4, '18710**********1', 'Prof. Dr. Ahmad Syarifuddin', 'Bandar Lampung', '1970-01-15', 'Dosen', 'UIN Raden Intan Lampung', 'Jl. Soekarno Hatta No. 123', '08123456789', 'undangan', 13),
(5, '18710**********2', 'Dr. Hj. Fatimah Al-Zahra', 'Metro', '1975-03-20', 'Dosen', 'IAIN Metro', 'Jl. Ki Hajar Dewantara No. 456', '08123456790', 'undangan', 14),
(6, '18030**********3', 'Ustadz Muhammad Ridwan', 'Lampung Tengah', '1980-08-10', 'Guru', 'MAN 1 Lampung Tengah', 'Jl. Raya Gunung Sugih No. 789', '08123456791', 'kabupaten', 3);

-- Insert sample pendidikan dewan hakim
INSERT INTO dewan_hakim_pendidikan (id_dewan_hakim, jenjang, instansi, jurusan, tahun_mulai, tahun_lulus) VALUES
(1, 'S3', 'UIN Syarif Hidayatullah Jakarta', 'Ilmu Al-Quran dan Tafsir', 2005, 2008),
(1, 'S2', 'UIN Syarif Hidayatullah Jakarta', 'Ilmu Al-Quran dan Tafsir', 2000, 2003),
(1, 'S1', 'IAIN Raden Intan Lampung', 'Ilmu Al-Quran dan Tafsir', 1992, 1996),
(2, 'S3', 'UIN Sunan Kalijaga Yogyakarta', 'Studi Islam', 2008, 2012),
(2, 'S2', 'UIN Sunan Kalijaga Yogyakarta', 'Studi Islam', 2003, 2006),
(2, 'S1', 'IAIN Raden Intan Lampung', 'Dakwah', 1995, 1999);

-- Insert sample pengalaman dewan hakim
INSERT INTO dewan_hakim_pengalaman (id_dewan_hakim, nama_kegiatan, penyelenggara, tahun, tingkat) VALUES
(1, 'MTQ Nasional XXVIII', 'Kemenag RI', 2020, 'nasional'),
(1, 'MTQ Provinsi Lampung', 'Kemenag Provinsi Lampung', 2019, 'provinsi'),
(1, 'STQ Nasional', 'Kemenag RI', 2018, 'nasional'),
(2, 'MTQ Nasional XXVII', 'Kemenag RI', 2018, 'nasional'),
(2, 'MTQ Provinsi Lampung', 'Kemenag Provinsi Lampung', 2017, 'provinsi'),
(3, 'MTQ Provinsi Lampung', 'Kemenag Provinsi Lampung', 2019, 'provinsi'),
(3, 'MTQ Kabupaten Lampung Tengah', 'Kemenag Lampung Tengah', 2018, 'kabupaten');

-- Set database charset and collation
ALTER DATABASE mtq_lampung CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- =============================================
-- SECURITY SETTINGS
-- =============================================

-- Create database user for application
-- CREATE USER 'mtq_user'@'localhost' IDENTIFIED BY 'mtq_password_2024!';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON mtq_lampung.* TO 'mtq_user'@'localhost';
-- FLUSH PRIVILEGES;

-- =============================================
-- BACKUP AND MAINTENANCE
-- =============================================

-- Event scheduler untuk cleanup log lama (optional)
-- CREATE EVENT ev_cleanup_log
-- ON SCHEDULE EVERY 1 MONTH
-- DO
--   DELETE FROM log_aktivitas WHERE created_at < DATE_SUB(NOW(), INTERVAL 6 MONTH);

-- =============================================
-- SCHEMA COMPLETED
-- =============================================

-- Tambahan untuk optimasi query yang sering digunakan
-- Query untuk cek status pendaftaran
CREATE INDEX idx_pendaftaran_status_tahun ON pendaftaran(status_pendaftaran, tahun_pendaftaran);

-- Query untuk laporan per wilayah
CREATE INDEX idx_peserta_wilayah_status ON peserta(id_wilayah, status_peserta);

-- Query untuk pencarian peserta
CREATE INDEX idx_peserta_nama_nik ON peserta(nama_lengkap, nik);

-- Query untuk notifikasi
CREATE INDEX idx_notifikasi_user_read ON notifikasi(id_user, is_read);

-- Query untuk pembayaran
CREATE INDEX idx_pembayaran_tanggal ON pembayaran(tanggal_bayar);

-- =============================================
-- FINAL COMMENTS
-- =============================================

/*
DATABASE SCHEMA MTQ LAMPUNG - VERSION 2.0
===========================================

Fitur yang tersedia:
1. Multi-role user management (superadmin, admin, admin_daerah, peserta, dewan_hakim)
2. Manajemen wilayah hierarkis
3. Manajemen cabang lomba dan golongan
4. Pendaftaran peserta dengan workflow approval
5. Upload dan verifikasi dokumen
6. Sistem pembayaran multi-metode
7. Penilaian dengan multiple jenis nilai
8. Ranking otomatis
9. Notifikasi system
10. Audit trail lengkap
11. Dashboard dan laporan
12. Backup dan maintenance tools

Keamanan:
- Password hashing
- Role-based access control
- Audit logging
- Data validation constraints
- SQL injection prevention

Performa:
- Optimized indexes
- Efficient queries
- Proper normalization
- Caching-friendly structure

Maintenance:
- Auto cleanup procedures
- Backup strategies
- Monitoring capabilities
- Error handling

Total Tables: 20
Total Views: 5
Total Functions: 5
Total Procedures: 3
Total Triggers: 5
*/

